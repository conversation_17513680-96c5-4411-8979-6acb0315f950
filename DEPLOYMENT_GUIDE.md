# FinScan AI - Firebase Deployment Guide

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Firebase CLI** installed globally: `npm install -g firebase-tools`
3. **Google Cloud Account** with billing enabled
4. **Gemini API Key** from Google AI Studio

## Step-by-Step Deployment

### 1. Firebase Project Setup

```bash
# Login to Firebase
firebase login

# Initialize Firebase project (if not already done)
firebase init

# Select the following services:
# - Firestore
# - Functions
# - Hosting
# - Storage
```

### 2. Configure Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing one
3. Enable the following services:
   - Authentication (Email/Password)
   - Firestore Database
   - Storage
   - Functions

### 3. Update Firebase Configuration

1. Copy your Firebase config from Project Settings > General > Your apps
2. Update `firebase.ts` with your actual configuration:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

### 4. Environment Variables Setup

1. Copy `.env.local.example` to `.env.local`
2. Fill in your actual values:
   - Get Gemini API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Use your Firebase configuration values

### 5. Cloud Functions Configuration

```bash
# Navigate to functions directory
cd functions

# Install dependencies
npm install

# Set Gemini API key for Cloud Functions
firebase functions:config:set gemini.key="your_gemini_api_key_here"

# Build functions
npm run build
```

### 6. Deploy Security Rules

```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Storage rules
firebase deploy --only storage:rules
```

### 7. Build and Deploy Application

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Deploy everything
firebase deploy

# Or deploy specific services:
firebase deploy --only hosting
firebase deploy --only functions
```

### 8. Enable Authentication

1. Go to Firebase Console > Authentication > Sign-in method
2. Enable "Email/Password" provider
3. Configure authorized domains if needed

### 9. Test Deployment

1. Visit your deployed URL
2. Test user registration/login
3. Test account creation
4. Test transaction creation
5. Test receipt scanning functionality

## Environment Variables Reference

### Frontend (.env.local)
```
GEMINI_API_KEY=your_gemini_api_key
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

### Cloud Functions
```bash
firebase functions:config:set gemini.key="your_gemini_api_key"
```

## Security Checklist

- [ ] Firestore security rules deployed
- [ ] Storage security rules deployed
- [ ] Authentication enabled
- [ ] API keys properly configured
- [ ] HTTPS enforced
- [ ] Authorized domains configured

## Monitoring and Maintenance

1. **Firebase Console**: Monitor usage, errors, and performance
2. **Cloud Functions Logs**: `firebase functions:log`
3. **Analytics**: Enable Firebase Analytics for user insights
4. **Alerts**: Set up billing alerts and quota monitoring

## Troubleshooting

### Common Issues

1. **Functions not deploying**: Check Node.js version (must be 18+)
2. **Authentication errors**: Verify authorized domains
3. **Firestore permission denied**: Check security rules
4. **Receipt scanning fails**: Verify Gemini API key configuration

### Debug Commands

```bash
# Check Firebase project
firebase projects:list

# Test functions locally
firebase emulators:start

# View function logs
firebase functions:log

# Check configuration
firebase functions:config:get
```

## Production Considerations

1. **Backup Strategy**: Set up automated Firestore backups
2. **Monitoring**: Implement error tracking (Sentry, etc.)
3. **Performance**: Monitor function execution times
4. **Costs**: Monitor Firebase usage and billing
5. **Updates**: Plan for regular dependency updates

## Support

For issues with:
- **Firebase**: [Firebase Support](https://firebase.google.com/support)
- **Gemini API**: [Google AI Studio](https://aistudio.google.com/)
- **Application**: Check the comprehensive analysis document
