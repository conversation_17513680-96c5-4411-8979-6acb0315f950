import React, { useState, useMemo, useCallback } from 'react';
import { Transaction, Subscription, Account } from '../../types';
import { useDebounce } from '../../hooks/useDebounce';
import { TRANSACTIONS_PER_PAGE, SUBSCRIPTIONS_PER_PAGE } from '../../constants';
import { SearchIcon } from '../Icons';
import { TransactionItem } from '../Dashboard/TransactionItem';
import { SubscriptionItem } from './SubscriptionItem';

interface TransactionsScreenProps {
  transactions: Transaction[];
  subscriptions: Subscription[];
  accounts: Account[];
  openTransactionModal: (tx?: Transaction) => void;
  handleDeleteTransaction: (id: string) => void;
  openSubscriptionModal: (sub?: Subscription) => void;
  handleDeleteSubscription: (id: string) => void;
  formatCurrency: (amount: number) => string;
}

export const TransactionsScreen: React.FC<TransactionsScreenProps> = React.memo(({
  transactions,
  subscriptions,
  accounts,
  openTransactionModal,
  handleDeleteTransaction,
  openSubscriptionModal,
  handleDeleteSubscription,
  formatCurrency
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [view, setView] = useState<'transactions' | 'subscriptions'>('transactions');
  const [transactionPage, setTransactionPage] = useState(1);
  const [subscriptionPage, setSubscriptionPage] = useState(1);

  const filteredTransactions = useMemo(() => {
    if (!debouncedSearchQuery) {
      return transactions;
    }
    const lowercasedQuery = debouncedSearchQuery.toLowerCase();
    return transactions.filter(tx => {
      const account = accounts.find(a => a.id === tx.accountId);
      const accountName = account ? account.name.toLowerCase() : '';

      const descriptionMatch = tx.description.toLowerCase().includes(lowercasedQuery);
      const accountMatch = accountName.includes(lowercasedQuery);
      const amountMatch = tx.amount.toString().includes(lowercasedQuery);
      
      return descriptionMatch || accountMatch || amountMatch;
    });
  }, [transactions, accounts, debouncedSearchQuery]);

  const sortedSubscriptions = useMemo(() => {
    return [...subscriptions].sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
  }, [subscriptions]);

  // Pagination logic
  const paginatedTransactions = useMemo(() => {
    const startIndex = (transactionPage - 1) * TRANSACTIONS_PER_PAGE;
    return filteredTransactions.slice(startIndex, startIndex + TRANSACTIONS_PER_PAGE);
  }, [filteredTransactions, transactionPage]);

  const paginatedSubscriptions = useMemo(() => {
    const startIndex = (subscriptionPage - 1) * SUBSCRIPTIONS_PER_PAGE;
    return sortedSubscriptions.slice(startIndex, startIndex + SUBSCRIPTIONS_PER_PAGE);
  }, [sortedSubscriptions, subscriptionPage]);

  const totalTransactionPages = Math.ceil(filteredTransactions.length / TRANSACTIONS_PER_PAGE);
  const totalSubscriptionPages = Math.ceil(sortedSubscriptions.length / SUBSCRIPTIONS_PER_PAGE);

  const handleViewChange = useCallback((newView: 'transactions' | 'subscriptions') => {
    setView(newView);
    setSearchQuery(''); // Clear search when switching views
  }, []);

  const handleTransactionPageChange = useCallback((page: number) => {
    setTransactionPage(page);
  }, []);

  const handleSubscriptionPageChange = useCallback((page: number) => {
    setSubscriptionPage(page);
  }, []);

  const renderPagination = (currentPage: number, totalPages: number, onPageChange: (page: number) => void) => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center items-center space-x-2 mt-6">
        <button
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="px-3 py-2 rounded-lg bg-gray-800 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
        >
          Previous
        </button>
        
        <span className="text-gray-400 text-sm">
          Page {currentPage} of {totalPages}
        </span>
        
        <button
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className="px-3 py-2 rounded-lg bg-gray-800 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
        >
          Next
        </button>
      </div>
    );
  };

  return (
    <div className="animate-fade-in">
      <h1 className="text-2xl font-bold mb-4 text-gray-100">
        {view === 'transactions' ? 'All Transactions' : 'Recurring Subscriptions'}
      </h1>

      <div className="grid grid-cols-2 gap-2 mb-4 bg-gray-900 p-1 rounded-lg">
        <button 
          type="button" 
          onClick={() => handleViewChange('transactions')} 
          className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${
            view === 'transactions' ? 'bg-gray-700 shadow' : 'text-gray-400'
          }`}
        >
          Transactions
        </button>
        <button 
          type="button" 
          onClick={() => handleViewChange('subscriptions')} 
          className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${
            view === 'subscriptions' ? 'bg-gray-700 shadow' : 'text-gray-400'
          }`}
        >
          Subscriptions
        </button>
      </div>

      {view === 'transactions' && (
        <>
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search transactions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>

          <div className="space-y-2">
            {paginatedTransactions.length > 0 ? (
              paginatedTransactions.map((tx) => (
                <TransactionItem
                  key={tx.id}
                  transaction={tx}
                  account={accounts.find(a => a.id === tx.accountId)}
                  onClick={() => openTransactionModal(tx)}
                  formatCurrency={formatCurrency}
                />
              ))
            ) : (
              <p className="text-center text-gray-500 py-8">
                {searchQuery ? 'No transactions found matching your search.' : 'No transactions yet.'}
              </p>
            )}
          </div>

          {renderPagination(transactionPage, totalTransactionPages, handleTransactionPageChange)}
        </>
      )}

      {view === 'subscriptions' && (
        <>
          <div className="space-y-2">
            {paginatedSubscriptions.length > 0 ? (
              paginatedSubscriptions.map((sub) => (
                <SubscriptionItem
                  key={sub.id}
                  subscription={sub}
                  account={accounts.find(a => a.id === sub.accountId)}
                  onClick={() => openSubscriptionModal(sub)}
                  formatCurrency={formatCurrency}
                />
              ))
            ) : (
              <p className="text-center text-gray-500 py-8">No subscriptions yet.</p>
            )}
          </div>

          {renderPagination(subscriptionPage, totalSubscriptionPages, handleSubscriptionPageChange)}
        </>
      )}
    </div>
  );
});

TransactionsScreen.displayName = 'TransactionsScreen';
