import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { db } from './firebase';
import { collection, onSnapshot, doc, addDoc, updateDoc, deleteDoc, query, orderBy, writeBatch } from 'firebase/firestore';
import { Account, Transaction, AccountType, TransactionType, GeminiScanResult, Currency, Subscription, SubscriptionFrequency } from './types';
import { EXPENSE_CATEGORIES, INCOME_CATEGORIES, RECENT_TRANSACTIONS_LIMIT } from './constants';
import { scanReceiptWithFirebase } from './services/geminiService';
import { validateData, transactionSchema, accountSchema, subscriptionSchema } from './utils/validation';
import { CameraIcon, ReceiptIcon, RepeatIcon, LogoutIcon } from './components/Icons';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { AuthScreen } from './components/AuthScreen';
import ErrorBoundary from './components/ErrorBoundary';
import { DashboardScreen } from './components/Dashboard/DashboardScreen';
import { BottomNav } from './components/Common/BottomNav';
import { TransactionsScreen } from './components/Transactions/TransactionsScreen';


function MainApp() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [currency, setCurrency] = useState<Currency>(Currency.USD);
  const [isLoading, setIsLoading] = useState(true);

  const [modal, setModal] = useState<string | null>(null);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | Partial<Transaction> | null>(null);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null);

  // Effect for fetching all data from Firestore
  useEffect(() => {
    if (!user) {
      setAccounts([]);
      setTransactions([]);
      setSubscriptions([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    
    // User settings (currency)
    const userDocUnsub = onSnapshot(doc(db, 'users', user.uid), (doc) => {
        if (doc.exists()) {
            setCurrency(doc.data().currency || Currency.USD);
        }
    });

    // Accounts
    const accountsUnsub = onSnapshot(collection(db, `users/${user.uid}/accounts`), (snapshot) => {
        const fetchedAccounts = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Account));
        setAccounts(fetchedAccounts);
    });

    // Transactions
    const transactionsQuery = query(collection(db, `users/${user.uid}/transactions`), orderBy('date', 'desc'));
    const transactionsUnsub = onSnapshot(transactionsQuery, (snapshot) => {
        const fetchedTransactions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Transaction));
        setTransactions(fetchedTransactions);
    });
    
    // Subscriptions
    const subscriptionsUnsub = onSnapshot(collection(db, `users/${user.uid}/subscriptions`), (snapshot) => {
        const fetchedSubscriptions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Subscription));
        setSubscriptions(fetchedSubscriptions);
        setIsLoading(false); // Consider loading finished after the largest collection is fetched
    });

    return () => {
        userDocUnsub();
        accountsUnsub();
        transactionsUnsub();
        subscriptionsUnsub();
    };
  }, [user]);
  
  // Effect to process subscriptions
  useEffect(() => {
    if (!user || subscriptions.length === 0 || isLoading) return;

    const processDueSubscriptions = async () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const batch = writeBatch(db);
      let hasUpdates = false;

      subscriptions.forEach(sub => {
        let nextPaymentDate = new Date(sub.startDate);
        if (isNaN(nextPaymentDate.getTime())) return;
        nextPaymentDate.setHours(0, 0, 0, 0);

        let subHasBeenUpdated = false;

        while (nextPaymentDate <= today) {
          hasUpdates = true;
          subHasBeenUpdated = true;
          
          // 1. Create a new transaction for this payment
          const newTxDocRef = doc(collection(db, `users/${user.uid}/transactions`));
          batch.set(newTxDocRef, {
            accountId: sub.accountId,
            type: TransactionType.Expense,
            amount: sub.amount,
            category: sub.category,
            description: `(Sub) ${sub.description}`,
            date: nextPaymentDate.toISOString(),
          });
          
          // 2. Calculate the next payment date
          if (sub.frequency === SubscriptionFrequency.Weekly) {
            nextPaymentDate.setDate(nextPaymentDate.getDate() + 7);
          } else {
            nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
          }
        }

        // 3. Update the subscription with the new start date
        if (subHasBeenUpdated) {
          const subDocRef = doc(db, `users/${user.uid}/subscriptions`, sub.id);
          batch.update(subDocRef, { startDate: nextPaymentDate.toISOString().split('T')[0] });
        }
      });
      
      if (hasUpdates) {
          try {
              await batch.commit();
          } catch (error) {
              // Log error in development only
              if (process.env.NODE_ENV === 'development') {
                console.error("Error processing subscriptions:", error);
              }
          }
      }
    };
    
    processDueSubscriptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscriptions, user, isLoading]);

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat(undefined, { style: 'currency', currency }).format(amount);
  }, [currency]);

  const calculateBalance = useCallback((accountId: string) => {
    const account = accounts.find(a => a.id === accountId);
    if (!account) return 0;
    return transactions
      .filter(t => t.accountId === accountId)
      .reduce((acc, t) => t.type === TransactionType.Income ? acc + t.amount : acc - t.amount, account.initialBalance);
  }, [accounts, transactions]);
  
  const totalBalance = useMemo(() => {
    return accounts.reduce((total, acc) => total + calculateBalance(acc.id), 0);
  }, [accounts, calculateBalance]);

  // Data Handlers
  const handleAddTransaction = async (tx: Omit<Transaction, 'id'>) => {
    try {
      await addDoc(collection(db, `users/${user!.uid}/transactions`), tx);
      setModal(null);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error adding transaction:', error);
      }
      alert('Failed to add transaction. Please try again.');
    }
  };
  const handleUpdateTransaction = async (tx: Transaction) => {
    try {
      const txDocRef = doc(db, `users/${user!.uid}/transactions`, tx.id);
      const { id: _id, ...data } = tx;
      await updateDoc(txDocRef, data);
      setEditingTransaction(null);
      setModal(null);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating transaction:', error);
      }
      alert('Failed to update transaction. Please try again.');
    }
  };
  const handleDeleteTransaction = async (id: string) => {
    if (confirm('Are you sure you want to permanently delete this transaction?')) {
      try {
        await deleteDoc(doc(db, `users/${user!.uid}/transactions`, id));
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error deleting transaction:', error);
        }
        alert('Failed to delete transaction. Please try again.');
      }
    }
  };
  const handleAddAccount = async (acc: Omit<Account, 'id'>) => {
    try {
      await addDoc(collection(db, `users/${user!.uid}/accounts`), acc);
      setModal(null);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error adding account:', error);
      }
      alert('Failed to add account. Please try again.');
    }
  };
  const handleUpdateAccount = async (acc: Account) => {
    try {
      const accDocRef = doc(db, `users/${user!.uid}/accounts`, acc.id);
      const { id: _id, ...data } = acc;
      await updateDoc(accDocRef, data);
      setEditingAccount(null);
      setModal(null);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating account:', error);
      }
      alert('Failed to update account. Please try again.');
    }
  };
  const handleDeleteAccount = async (id: string) => {
    if(confirm('Delete this account? This will also delete all associated transactions.')) {
        try {
          // Use a batch write to delete account and transactions atomically
          const batch = writeBatch(db);
          const accDocRef = doc(db, `users/${user!.uid}/accounts`, id);
          batch.delete(accDocRef);
          // This is inefficient for large datasets, but fine for a single user.
          // A better approach would be a Cloud Function.
          transactions.filter(t => t.accountId === id).forEach(t => {
              const txDocRef = doc(db, `users/${user!.uid}/transactions`, t.id);
              batch.delete(txDocRef);
          });
          await batch.commit();
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error deleting account:', error);
          }
          alert('Failed to delete account. Please try again.');
        }
    }
  };
  const handleAddSubscription = async (sub: Omit<Subscription, 'id'>) => {
      try {
        await addDoc(collection(db, `users/${user!.uid}/subscriptions`), sub);
        setModal(null);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error adding subscription:', error);
        }
        alert('Failed to add subscription. Please try again.');
      }
  };
  const handleUpdateSubscription = async (sub: Subscription) => {
      try {
        const subDocRef = doc(db, `users/${user!.uid}/subscriptions`, sub.id);
        const { id: _id, ...data } = sub;
        await updateDoc(subDocRef, data);
        setEditingSubscription(null);
        setModal(null);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error updating subscription:', error);
        }
        alert('Failed to update subscription. Please try again.');
      }
  };
  const handleDeleteSubscription = async (id: string) => {
      if(confirm('Are you sure you want to delete this subscription?')) {
          try {
            await deleteDoc(doc(db, `users/${user!.uid}/subscriptions`, id));
          } catch (error) {
            if (process.env.NODE_ENV === 'development') {
              console.error('Error deleting subscription:', error);
            }
            alert('Failed to delete subscription. Please try again.');
          }
      }
  };
  const handleSetCurrency = async (newCurrency: Currency) => {
      try {
        setCurrency(newCurrency);
        await updateDoc(doc(db, 'users', user!.uid), { currency: newCurrency });
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error updating currency:', error);
        }
        alert('Failed to update currency. Please try again.');
        // Revert the local state change
        setCurrency(currency);
      }
  };

  const openTransactionModal = (tx?: Transaction) => {
    setEditingTransaction(tx || null);
    setModal('transaction');
  };
  const openAccountModal = (acc?: Account) => {
      setEditingAccount(acc || null);
      setModal('account');
  };
  const openSubscriptionModal = (sub?: Subscription) => {
      setEditingSubscription(sub || null);
      setModal('subscription');
  };
  
  if (isLoading) {
      return (
          <div className="min-h-screen bg-black flex items-center justify-center text-white">
              <svg className="animate-spin h-8 w-8 text-indigo-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
          </div>
      );
  }
  
  const renderScreen = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardScreen accounts={accounts} calculateBalance={calculateBalance} totalBalance={totalBalance} transactions={transactions.slice(0, RECENT_TRANSACTIONS_LIMIT)} openTransactionModal={openTransactionModal} formatCurrency={formatCurrency} />;
      case 'transactions':
        return <TransactionsScreen transactions={transactions} subscriptions={subscriptions} accounts={accounts} openTransactionModal={openTransactionModal} handleDeleteTransaction={handleDeleteTransaction} openSubscriptionModal={openSubscriptionModal} handleDeleteSubscription={handleDeleteSubscription} formatCurrency={formatCurrency} />;
      case 'settings':
        return <SettingsScreen accounts={accounts} openAccountModal={openAccountModal} handleDeleteAccount={handleDeleteAccount} currency={currency} setCurrency={handleSetCurrency} />;
      default:
        return <DashboardScreen accounts={accounts} calculateBalance={calculateBalance} totalBalance={totalBalance} transactions={transactions.slice(0, RECENT_TRANSACTIONS_LIMIT)} openTransactionModal={openTransactionModal} formatCurrency={formatCurrency} />;
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col font-sans">
      <main className="flex-1 pb-24 overflow-y-auto">
        <div className="max-w-md mx-auto p-4">
          {renderScreen()}
        </div>
      </main>

      {modal === 'addChoice' && <AddChoiceModal onClose={() => setModal(null)} onAddTransaction={() => { setModal('transaction'); }} onAddSubscription={() => { setModal('subscription'); }} />}
      {modal === 'transaction' && <TransactionModal accounts={accounts} transaction={editingTransaction} onSave={editingTransaction?.id ? handleUpdateTransaction : handleAddTransaction} onClose={() => { setModal(null); setEditingTransaction(null); }} currency={currency} />}
      {modal === 'scan' && <ScanModal accounts={accounts} onClose={() => setModal(null)} onScanComplete={(data, accountId) => {
          const newTx: Omit<Transaction, 'id'> = {
              accountId,
              type: TransactionType.Expense,
              amount: data.totalAmount,
              category: data.category,
              description: data.merchantName,
              date: new Date(data.transactionDate).toISOString(),
          };
          setEditingTransaction(newTx); 
          setModal('transaction');
      }} />}
      {modal === 'account' && <AccountModal account={editingAccount} onSave={editingAccount ? handleUpdateAccount : handleAddAccount} onClose={() => { setModal(null); setEditingAccount(null); }} />}
      {modal === 'subscription' && <SubscriptionModal accounts={accounts} subscription={editingSubscription} onSave={editingSubscription ? handleUpdateSubscription : handleAddSubscription} onClose={() => { setModal(null); setEditingSubscription(null); }} currency={currency} />}

      <BottomNav activeTab={activeTab} setActiveTab={setActiveTab} onScanClick={() => setModal('scan')} onAddClick={() => setModal('addChoice')}/>
    </div>
  );
}

// App component orchestrates the entire application
export default function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ErrorBoundary>
  );
}

const AppContent = () => {
    const { user, loading } = useAuth();

    if (loading) {
        return (
          <div className="min-h-screen bg-black flex items-center justify-center text-white">
              <svg className="animate-spin h-8 w-8 text-indigo-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
          </div>
        );
    }
    
    return user ? <MainApp /> : <AuthScreen />;
};


interface SettingsScreenProps {
    accounts: Account[];
    openAccountModal: (acc?: Account) => void;
    handleDeleteAccount: (id: string) => void;
    currency: Currency;
    setCurrency: (currency: Currency) => void;
}






// Settings Screen
const SettingsScreen: React.FC<SettingsScreenProps> = ({ accounts, openAccountModal, handleDeleteAccount, currency, setCurrency }) => {
    const { logout, user } = useAuth();
    
    return (
        <div className="animate-fade-in">
            <h1 className="text-2xl font-bold mb-4 text-gray-100">Settings</h1>
            
            <div className="mb-6 p-4 bg-gray-900 rounded-xl border border-gray-800">
                <p className="text-sm text-gray-400">Logged in as</p>
                <p className="font-semibold text-gray-200">{user?.email}</p>
            </div>

            <div className="bg-gray-900 rounded-xl border border-gray-800">
                <div className="p-4 border-b border-gray-800 flex justify-between items-center">
                    <h2 className="font-semibold text-gray-200">Accounts</h2>
                    <button onClick={() => openAccountModal()} className="bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm font-semibold hover:bg-indigo-500 transition-colors">Add New</button>
                </div>
                <ul className="divide-y divide-gray-800">
                    {accounts.map((acc) => (
                        <li key={acc.id} className="p-4 flex justify-between items-center group">
                            <div>
                                <p className="font-medium text-gray-200">{acc.name}</p>
                                <p className="text-sm text-gray-400">{acc.type}</p>
                            </div>
                            <div className="flex items-center space-x-4 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button onClick={() => openAccountModal(acc)} className="text-indigo-400 text-sm font-semibold">Edit</button>
                                <button onClick={() => handleDeleteAccount(acc.id)} className="text-red-400 text-sm font-semibold">Delete</button>
                            </div>
                        </li>
                    ))}
                     {accounts.length === 0 && <li className="p-4 text-center text-sm text-gray-500">No accounts created.</li>}
                </ul>
            </div>
            <div className="mt-6 bg-gray-900 rounded-xl border border-gray-800 p-4">
                <label htmlFor="currency-select" className="block font-semibold text-gray-200 mb-2">Currency</label>
                <select 
                    id="currency-select" 
                    value={currency} 
                    onChange={e => setCurrency(e.target.value as Currency)}
                    className="w-full bg-gray-800 border-gray-700 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white"
                >
                    {Object.values(Currency).map(c => <option key={c} value={c}>{c}</option>)}
                </select>
            </div>
            <div className="mt-6">
                <button 
                    onClick={logout} 
                    className="w-full flex items-center justify-center space-x-2 bg-gray-800 text-red-400 font-semibold py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors"
                >
                    <LogoutIcon className="w-5 h-5" />
                    <span>Logout</span>
                </button>
            </div>
        </div>
    );
};









// Scan Modal
const ScanModal = ({ accounts, onClose, onScanComplete }: { accounts: Account[]; onClose: () => void; onScanComplete: (data: GeminiScanResult, accountId: string) => void; }) => {
  const { user } = useAuth();
  const [image, setImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState('Analyzing...');
  const [error, setError] = useState<string | null>(null);
  const [selectedAccountId, setSelectedAccountId] = useState<string>(accounts[0]?.id || '');
  
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleScan = async () => {
    if (!imageFile || !selectedAccountId || !user) {
      setError("Please select an image and an account.");
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      setLoadingText('Uploading...');
      const result = await scanReceiptWithFirebase(imageFile, user.uid, (progress) => {
          if (progress < 100) {
              setLoadingText(`Uploading... ${Math.round(progress)}%`);
          } else {
              setLoadingText('Analyzing...');
          }
      });
      onScanComplete(result, selectedAccountId);
    } catch (e: unknown) {
      setError(e.message || "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };
  
  const ModalWrapper: React.FC<{children: React.ReactNode}> = ({children}) => (
      <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
          <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm p-6 text-center border border-gray-700">
              {children}
          </div>
      </div>
  );

  if (accounts.length === 0) {
      return (
        <ModalWrapper>
            <h2 className="text-xl font-bold mb-2 text-yellow-400">No Account Found</h2>
            <p className="text-gray-400 mb-4">Please add an account in Settings before scanning a receipt.</p>
            <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">Close</button>
        </ModalWrapper>
      );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
        <div className="p-6">
          <h2 className="text-xl font-bold text-center mb-4 text-gray-100">Scan Receipt</h2>
          {error && <p className="bg-red-500/10 text-red-400 p-3 rounded-lg text-sm mb-4">{error}</p>}
          
          <div className="mb-4">
              <label htmlFor="account" className="block text-sm font-medium text-gray-400 mb-1">Deposit to Account</label>
              <select id="account" value={selectedAccountId} onChange={e => setSelectedAccountId(e.target.value)} className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                  {accounts.map(acc => <option key={acc.id} value={acc.id}>{acc.name}</option>)}
              </select>
          </div>
          
          {image ? (
            <div className="mb-4">
              <img src={image} alt="Receipt preview" className="rounded-lg max-h-60 w-full object-contain mb-2" />
              <button onClick={() => { setImage(null); setImageFile(null); if(fileInputRef.current) fileInputRef.current.value = ""; }} className="w-full text-sm text-indigo-400 hover:underline">Choose another</button>
            </div>
          ) : (
            <div className="mb-4">
              <input type="file" accept="image/*" capture="environment" onChange={handleFileChange} className="hidden" ref={fileInputRef} />
              <button onClick={() => fileInputRef.current?.click()} className="w-full border-2 border-dashed border-gray-600 rounded-lg p-8 flex flex-col items-center justify-center text-gray-400 hover:bg-gray-700/50 hover:border-indigo-500 transition-colors">
                <CameraIcon className="w-10 h-10 mb-2" />
                <span className="font-semibold">Capture or Upload</span>
              </button>
            </div>
          )}

          <div className="space-y-2">
            <button onClick={handleScan} disabled={!image || isLoading} className="w-full bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 disabled:bg-indigo-400/50 disabled:cursor-not-allowed flex items-center justify-center transition-colors">
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                  {loadingText}
                </>
              ) : "Process Receipt"}
            </button>
            <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  );
};


// Transaction Modal
interface TransactionModalProps {
  accounts: Account[];
  transaction: Transaction | Partial<Transaction> | null;
  onSave: (transaction: Omit<Transaction, 'id'> | Transaction) => void;
  onClose: () => void;
  currency: Currency;
}
const TransactionModal: React.FC<TransactionModalProps> = ({ accounts, transaction, onSave, onClose, currency }) => {
    const isEditing = !!transaction?.id;
    const [txData, setTxData] = useState({
        accountId: transaction?.accountId || accounts[0]?.id || '',
        type: transaction?.type || TransactionType.Expense,
        amount: transaction?.amount || '',
        category: transaction?.category || '',
        description: transaction?.description || '',
        date: transaction?.date ? new Date(transaction.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const handleChange = <K extends keyof typeof txData,>(key: K, value: (typeof txData)[K]) => {
        setTxData(prev => ({...prev, [key]: value}));
    };

    const handleTypeChange = (type: TransactionType) => {
        handleChange('type', type);
        handleChange('category', ''); // Reset category on type change
    };
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setValidationErrors({});

        const dataToValidate = {
            ...txData,
            amount: parseFloat(txData.amount.toString()) || 0,
            date: new Date(txData.date).toISOString()
        };

        const validation = validateData(transactionSchema, dataToValidate);

        if (!validation.isValid) {
            setValidationErrors(validation.errors);
            return;
        }

        onSave({ ...transaction, ...txData, amount: parseFloat(txData.amount.toString()) } as Transaction);
    };
    
    const categories = txData.type === TransactionType.Expense ? EXPENSE_CATEGORIES : INCOME_CATEGORIES;

    if (accounts.length === 0 && !txData.accountId) {
        return (
             <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
                <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm p-6 text-center border border-gray-700">
                    <h2 className="text-xl font-bold mb-2 text-yellow-400">No Account Found</h2>
                    <p className="text-gray-400 mb-4">Please add an account in Settings before adding a transaction.</p>
                    <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">Close</button>
                </div>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
                <div className="p-6">
                    <h2 className="text-xl font-bold text-center mb-4 text-gray-100">{isEditing ? 'Edit' : 'Add'} Transaction</h2>

                    <div className="grid grid-cols-2 gap-2 mb-4 bg-gray-900 p-1 rounded-lg">
                        <button type="button" onClick={() => handleTypeChange(TransactionType.Expense)} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${txData.type === TransactionType.Expense ? 'bg-gray-700 shadow' : 'text-gray-400'}`}>Expense</button>
                        <button type="button" onClick={() => handleTypeChange(TransactionType.Income)} className={`py-2 px-4 rounded-md text-sm font-semibold transition-colors ${txData.type === TransactionType.Income ? 'bg-gray-700 shadow' : 'text-gray-400'}`}>Income</button>
                    </div>

                    <div className="space-y-4">
                        <div>
                            <div className="relative">
                                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-2xl text-gray-500">{new Intl.NumberFormat(undefined, {style:'currency', currency, minimumFractionDigits:0}).format(0).replace(/[0-9]/g, '').trim()}</span>
                                <input type="number" step="0.01" placeholder="0.00" value={txData.amount} onChange={e => handleChange('amount', e.target.value)} required className="w-full text-4xl font-bold text-center border-none focus:ring-0 p-2 bg-transparent text-white" />
                            </div>
                            {validationErrors.amount && <p className="text-red-400 text-sm mt-1 text-center">{validationErrors.amount}</p>}
                        </div>
                        <div>
                            <input type="text" placeholder="Description" value={txData.description} onChange={e => handleChange('description', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                            {validationErrors.description && <p className="text-red-400 text-sm mt-1">{validationErrors.description}</p>}
                        </div>
                        <div>
                            <select value={txData.category} onChange={e => handleChange('category', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                <option value="" disabled>Select Category</option>
                                {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                            </select>
                            {validationErrors.category && <p className="text-red-400 text-sm mt-1">{validationErrors.category}</p>}
                        </div>
                        <div>
                            <select value={txData.accountId} onChange={e => handleChange('accountId', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                <option value="" disabled>Select Account</option>
                                {accounts.map(acc => <option key={acc.id} value={acc.id}>{acc.name}</option>)}
                            </select>
                            {validationErrors.accountId && <p className="text-red-400 text-sm mt-1">{validationErrors.accountId}</p>}
                        </div>
                        <div>
                            <input type="date" value={txData.date} onChange={e => handleChange('date', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                            {validationErrors.date && <p className="text-red-400 text-sm mt-1">{validationErrors.date}</p>}
                        </div>
                    </div>
                </div>
                <div className="bg-gray-800/50 p-4 grid grid-cols-2 gap-3 rounded-b-2xl border-t border-gray-700">
                    <button type="button" onClick={onClose} className="bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
                    <button type="submit" className="bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 transition-colors">Save</button>
                </div>
            </form>
        </div>
    );
};


// Account Modal
const AccountModal = ({ account, onSave, onClose }: { account: Account | null; onSave: (acc: Omit<Account, 'id'> | Account) => void; onClose: () => void; }) => {
    const isEditing = !!account;
    const [accData, setAccData] = useState({
        name: account?.name || '',
        type: account?.type || AccountType.Checking,
        initialBalance: account?.initialBalance ?? '',
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const handleChange = (key: keyof typeof accData, value: any) => {
        setAccData(prev => ({ ...prev, [key]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setValidationErrors({});

        const dataToValidate = {
            ...accData,
            initialBalance: parseFloat(String(accData.initialBalance)) || 0
        };

        const validation = validateData(accountSchema, dataToValidate);

        if (!validation.isValid) {
            setValidationErrors(validation.errors);
            return;
        }

        onSave({ ...account, ...accData, initialBalance: parseFloat(String(accData.initialBalance)) });
    };
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
                <div className="p-6">
                    <h2 className="text-xl font-bold text-center mb-6 text-gray-100">{isEditing ? 'Edit' : 'Add'} Account</h2>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-400">Name</label>
                            <input type="text" value={accData.name} onChange={e => handleChange('name', e.target.value)} required className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                            {validationErrors.name && <p className="text-red-400 text-sm mt-1">{validationErrors.name}</p>}
                        </div>
                         <div>
                            <label className="block text-sm font-medium text-gray-400">Type</label>
                            <select value={accData.type} onChange={e => handleChange('type', e.target.value)} required className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                {Object.values(AccountType).map(type => <option key={type} value={type}>{type}</option>)}
                            </select>
                            {validationErrors.type && <p className="text-red-400 text-sm mt-1">{validationErrors.type}</p>}
                        </div>
                         <div>
                            <label className="block text-sm font-medium text-gray-400">Initial Balance</label>
                            <input type="number" step="0.01" value={accData.initialBalance} onChange={e => handleChange('initialBalance', e.target.value)} required className="mt-1 w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                            {validationErrors.initialBalance && <p className="text-red-400 text-sm mt-1">{validationErrors.initialBalance}</p>}
                        </div>
                    </div>
                </div>
                <div className="bg-gray-800/50 p-4 grid grid-cols-2 gap-3 rounded-b-2xl border-t border-gray-700">
                    <button type="button" onClick={onClose} className="bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
                    <button type="submit" className="bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 transition-colors">Save</button>
                </div>
            </form>
        </div>
    );
};

// AddChoiceModal
const AddChoiceModal = ({ onClose, onAddTransaction, onAddSubscription }: { onClose: () => void; onAddTransaction: () => void; onAddSubscription: () => void; }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-end justify-center z-50 p-4 animate-fade-in" onClick={onClose}>
        <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700" onClick={e => e.stopPropagation()}>
            <div className="p-4 space-y-3">
                <button onClick={onAddTransaction} className="w-full flex items-center text-left p-4 bg-gray-900 hover:bg-gray-700 rounded-lg transition-colors">
                    <ReceiptIcon className="w-6 h-6 mr-4 text-indigo-400" />
                    <div>
                        <p className="font-semibold text-gray-100">New Transaction</p>
                        <p className="text-sm text-gray-400">Log a single expense or income.</p>
                    </div>
                </button>
                 <button onClick={onAddSubscription} className="w-full flex items-center text-left p-4 bg-gray-900 hover:bg-gray-700 rounded-lg transition-colors">
                    <RepeatIcon className="w-6 h-6 mr-4 text-purple-400" />
                    <div>
                        <p className="font-semibold text-gray-100">New Subscription</p>
                        <p className="text-sm text-gray-400">Add a recurring weekly/monthly bill.</p>
                    </div>
                </button>
            </div>
            <div className="p-2 border-t border-gray-700">
                 <button onClick={onClose} className="w-full bg-transparent text-gray-300 font-semibold py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">Cancel</button>
            </div>
        </div>
    </div>
  );
};

// Subscription Modal
const SubscriptionModal = ({ accounts, subscription, onSave, onClose, currency }: { accounts: Account[]; subscription: Subscription | null; onSave: (sub: Omit<Subscription, 'id'> | Subscription) => void; onClose: () => void; currency: Currency; }) => {
    const isEditing = !!subscription;
    const [subData, setSubData] = useState({
        accountId: subscription?.accountId || accounts[0]?.id || '',
        amount: subscription?.amount || '',
        category: subscription?.category || '',
        description: subscription?.description || '',
        frequency: subscription?.frequency || SubscriptionFrequency.Monthly,
        startDate: subscription?.startDate ? new Date(subscription.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const handleChange = <K extends keyof typeof subData,>(key: K, value: (typeof subData)[K]) => {
        setSubData(prev => ({...prev, [key]: value}));
    };
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setValidationErrors({});

        const dataToValidate = {
            ...subData,
            amount: parseFloat(subData.amount.toString()) || 0,
            startDate: new Date(subData.startDate).toISOString()
        };

        const validation = validateData(subscriptionSchema, dataToValidate);

        if (!validation.isValid) {
            setValidationErrors(validation.errors);
            return;
        }

        onSave({ ...subscription, ...subData, amount: parseFloat(subData.amount.toString()) } as Subscription);
    };

    if (accounts.length === 0 && !subData.accountId) {
        return (
             <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
                <div className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm p-6 text-center border border-gray-700">
                    <h2 className="text-xl font-bold mb-2 text-yellow-400">No Account Found</h2>
                    <p className="text-gray-400 mb-4">Please add an account in Settings before adding a subscription.</p>
                    <button onClick={onClose} className="w-full bg-gray-700 text-gray-200 font-semibold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">Close</button>
                </div>
            </div>
        );
    }
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 animate-fade-in">
            <form onSubmit={handleSubmit} className="bg-gray-800 rounded-2xl shadow-xl w-full max-w-sm border border-gray-700">
                <div className="p-6">
                    <h2 className="text-xl font-bold text-center mb-4 text-gray-100">{isEditing ? 'Edit' : 'Add'} Subscription</h2>
                    <div className="space-y-4">
                        <div>
                            <div className="relative">
                                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-2xl text-gray-500">{new Intl.NumberFormat(undefined, {style:'currency', currency, minimumFractionDigits:0}).format(0).replace(/[0-9]/g, '').trim()}</span>
                                <input type="number" step="0.01" placeholder="0.00" value={subData.amount} onChange={e => handleChange('amount', e.target.value)} required className="w-full text-4xl font-bold text-center border-none focus:ring-0 p-2 bg-transparent text-white" />
                            </div>
                            {validationErrors.amount && <p className="text-red-400 text-sm mt-1 text-center">{validationErrors.amount}</p>}
                        </div>
                        <div>
                            <input type="text" placeholder="Description (e.g. Netflix)" value={subData.description} onChange={e => handleChange('description', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                            {validationErrors.description && <p className="text-red-400 text-sm mt-1">{validationErrors.description}</p>}
                        </div>
                        <div>
                            <select value={subData.category} onChange={e => handleChange('category', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                <option value="" disabled>Select Category</option>
                                {EXPENSE_CATEGORIES.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                            </select>
                            {validationErrors.category && <p className="text-red-400 text-sm mt-1">{validationErrors.category}</p>}
                        </div>
                        <div>
                            <select value={subData.accountId} onChange={e => handleChange('accountId', e.target.value)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                <option value="" disabled>Select Account</option>
                                {accounts.map(acc => <option key={acc.id} value={acc.id}>{acc.name}</option>)}
                            </select>
                            {validationErrors.accountId && <p className="text-red-400 text-sm mt-1">{validationErrors.accountId}</p>}
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-xs font-medium text-gray-400 mb-1">Frequency</label>
                                <select value={subData.frequency} onChange={e => handleChange('frequency', e.target.value as SubscriptionFrequency)} required className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white">
                                    {Object.values(SubscriptionFrequency).map(freq => <option key={freq} value={freq}>{freq}</option>)}
                                </select>
                                {validationErrors.frequency && <p className="text-red-400 text-xs mt-1">{validationErrors.frequency}</p>}
                            </div>
                            <div>
                                <label className="block text-xs font-medium text-gray-400 mb-1">Next Payment</label>
                                <input type="date" value={subData.startDate} onChange={e => handleChange('startDate', e.target.value)} required title="Next Payment Date" className="w-full bg-gray-700 border-gray-600 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-white" />
                                {validationErrors.startDate && <p className="text-red-400 text-xs mt-1">{validationErrors.startDate}</p>}
                            </div>
                        </div>
                    </div>
                </div>
                <div className="bg-gray-800/50 p-4 grid grid-cols-2 gap-3 rounded-b-2xl border-t border-gray-700">
                    <button type="button" onClick={onClose} className="bg-gray-700 text-gray-200 font-semibold py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors">Cancel</button>
                    <button type="submit" className="bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-500 transition-colors">Save</button>
                </div>
            </form>
        </div>
    );
};
