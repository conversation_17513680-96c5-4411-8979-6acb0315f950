import React from 'react';
import { Transaction, Account, TransactionType } from '../../types';
import { ArrowDownIcon, ArrowUpIcon } from '../Icons';

interface TransactionItemProps {
  transaction: Transaction;
  account?: Account;
  onClick: () => void;
  formatCurrency: (amount: number) => string;
}

export const TransactionItem: React.FC<TransactionItemProps> = React.memo(({ 
  transaction, 
  account, 
  onClick, 
  formatCurrency 
}) => {
  const isIncome = transaction.type === TransactionType.Income;
  const sign = isIncome ? '+' : '-';
  const color = isIncome ? 'text-green-400' : 'text-gray-200';
  const Icon = isIncome ? ArrowDownIcon : ArrowUpIcon;
  const iconColor = isIncome ? 'bg-green-500/10 text-green-400' : 'bg-red-500/10 text-red-400';

  return (
    <button 
      onClick={onClick} 
      className="w-full text-left bg-gray-900 p-3 rounded-xl border border-gray-800 flex items-center space-x-4 hover:bg-gray-800 transition-colors duration-200"
    >
      <div className={`rounded-full p-2 ${iconColor}`}>
        <Icon className="w-5 h-5" />
      </div>
      <div className="flex-1">
        <p className="font-semibold text-gray-200">{transaction.description}</p>
        <p className="text-sm text-gray-400">
          {new Date(transaction.date).toLocaleDateString()} &bull; {transaction.category}
        </p>
      </div>
      <div className="text-right">
        <p className={`font-semibold font-mono ${color}`}>
          {sign}{formatCurrency(transaction.amount).replace(/[^0-9.,-]/g, '')}
        </p>
        {account && <p className="text-sm text-gray-500">{account.name}</p>}
      </div>
    </button>
  );
});

TransactionItem.displayName = 'TransactionItem';
