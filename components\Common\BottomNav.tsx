import React from 'react';
import { HomeIcon, ListIcon, CameraIcon, CogIcon, PlusIcon } from '../Icons';

interface BottomNavProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  onScanClick: () => void;
  onAddClick: () => void;
}

export const BottomNav: React.FC<BottomNavProps> = React.memo(({ 
  activeTab, 
  setActiveTab, 
  onScanClick, 
  onAddClick 
}) => {
  const navItems = [
    { id: 'dashboard', label: 'Home', icon: HomeIcon },
    { id: 'transactions', label: 'Transactions', icon: ListIcon },
    { id: 'add', label: 'Add', icon: PlusIcon, isCentral: true, action: onAddClick },
    { id: 'scan', label: 'Scan', icon: CameraIcon, action: onScanClick },
    { id: 'settings', label: 'Settings', icon: CogIcon },
  ];

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-gray-900/80 backdrop-blur-sm border-t border-gray-800">
      <nav className="flex justify-around items-center max-w-md mx-auto h-16">
        {navItems.map(item => {
          if (item.isCentral) {
            return (
              <button 
                key={item.id} 
                onClick={item.action} 
                className="-mt-8 bg-indigo-600 text-white rounded-full p-4 shadow-lg hover:bg-indigo-500 transition-transform transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500"
              >
                <item.icon className="w-7 h-7" />
              </button>
            );
          }
          return (
            <button
              key={item.id}
              onClick={() => item.action ? item.action() : setActiveTab(item.id)}
              className={`flex flex-col items-center justify-center w-full transition-colors duration-200 ${
                activeTab === item.id ? 'text-indigo-400' : 'text-gray-400 hover:text-indigo-400'
              }`}
            >
              <item.icon className="w-6 h-6 mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          );
        })}
      </nav>
    </footer>
  );
});

BottomNav.displayName = 'BottomNav';
