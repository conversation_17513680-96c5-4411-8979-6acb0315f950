import Joi from 'joi';
import { EXPENSE_CATEGORIES, INCOME_CATEGORIES, MAX_DESCRIPTION_LENGTH, MAX_AMOUNT, MAX_ACCOUNT_NAME_LENGTH } from '../constants';
import { AccountType, TransactionType, SubscriptionFrequency } from '../types';

// Transaction validation schema
export const transactionSchema = Joi.object({
  accountId: Joi.string().required().messages({
    'string.empty': 'Please select an account',
    'any.required': 'Account is required'
  }),
  type: Joi.string().valid(...Object.values(TransactionType)).required().messages({
    'any.only': 'Please select a valid transaction type',
    'any.required': 'Transaction type is required'
  }),
  amount: Joi.number().positive().max(MAX_AMOUNT).required().messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be positive',
    'number.max': `Amount cannot exceed ${MAX_AMOUNT.toLocaleString()}`,
    'any.required': 'Amount is required'
  }),
  description: Joi.string()
    .max(MAX_DESCRIPTION_LENGTH)
    .pattern(/^[a-zA-Z0-9\s\-_.,!?()]+$/)
    .required()
    .messages({
      'string.empty': 'Description is required',
      'string.max': `Description cannot exceed ${MAX_DESCRIPTION_LENGTH} characters`,
      'string.pattern.base': 'Description contains invalid characters',
      'any.required': 'Description is required'
    }),
  category: Joi.string().when('type', {
    is: TransactionType.Expense,
    then: Joi.string().valid(...EXPENSE_CATEGORIES).required(),
    otherwise: Joi.string().valid(...INCOME_CATEGORIES).required()
  }).messages({
    'any.only': 'Please select a valid category',
    'any.required': 'Category is required'
  }),
  date: Joi.date().iso().max('now').required().messages({
    'date.base': 'Please enter a valid date',
    'date.max': 'Date cannot be in the future',
    'any.required': 'Date is required'
  })
});

// Account validation schema
export const accountSchema = Joi.object({
  name: Joi.string()
    .max(MAX_ACCOUNT_NAME_LENGTH)
    .pattern(/^[a-zA-Z0-9\s\-_]+$/)
    .required()
    .messages({
      'string.empty': 'Account name is required',
      'string.max': `Account name cannot exceed ${MAX_ACCOUNT_NAME_LENGTH} characters`,
      'string.pattern.base': 'Account name contains invalid characters',
      'any.required': 'Account name is required'
    }),
  type: Joi.string().valid(...Object.values(AccountType)).required().messages({
    'any.only': 'Please select a valid account type',
    'any.required': 'Account type is required'
  }),
  initialBalance: Joi.number().max(MAX_AMOUNT).required().messages({
    'number.base': 'Initial balance must be a number',
    'number.max': `Initial balance cannot exceed ${MAX_AMOUNT.toLocaleString()}`,
    'any.required': 'Initial balance is required'
  })
});

// Subscription validation schema
export const subscriptionSchema = Joi.object({
  accountId: Joi.string().required().messages({
    'string.empty': 'Please select an account',
    'any.required': 'Account is required'
  }),
  amount: Joi.number().positive().max(MAX_AMOUNT).required().messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be positive',
    'number.max': `Amount cannot exceed ${MAX_AMOUNT.toLocaleString()}`,
    'any.required': 'Amount is required'
  }),
  description: Joi.string()
    .max(MAX_DESCRIPTION_LENGTH)
    .pattern(/^[a-zA-Z0-9\s\-_.,!?()]+$/)
    .required()
    .messages({
      'string.empty': 'Description is required',
      'string.max': `Description cannot exceed ${MAX_DESCRIPTION_LENGTH} characters`,
      'string.pattern.base': 'Description contains invalid characters',
      'any.required': 'Description is required'
    }),
  category: Joi.string().valid(...EXPENSE_CATEGORIES).required().messages({
    'any.only': 'Please select a valid category',
    'any.required': 'Category is required'
  }),
  frequency: Joi.string().valid(...Object.values(SubscriptionFrequency)).required().messages({
    'any.only': 'Please select a valid frequency',
    'any.required': 'Frequency is required'
  }),
  startDate: Joi.date().iso().required().messages({
    'date.base': 'Please enter a valid start date',
    'any.required': 'Start date is required'
  })
});

// Helper function to validate data and return formatted errors
export const validateData = <T>(schema: Joi.ObjectSchema<T>, data: any): { isValid: boolean; errors: Record<string, string>; data?: T } => {
  const { error, value } = schema.validate(data, { abortEarly: false });
  
  if (error) {
    const errors: Record<string, string> = {};
    error.details.forEach(detail => {
      const key = detail.path.join('.');
      errors[key] = detail.message;
    });
    return { isValid: false, errors };
  }
  
  return { isValid: true, errors: {}, data: value };
};
